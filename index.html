<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=1200" />
  <title>BASELINE — Diamond Training Log (Clean)</title>
  <!-- Self-hosted styles to avoid CSP issues on Netlify -->
  <style>
    /* CSS Variables */
    :root{
      --baseline-orange:#F26C1A;
      --baseline-cream:#F2D8B3;
      --baseline-dark:#0A0A0A;
      --baseline-background:#0A0A0A;
      --baseline-foreground:#F2D8B3;
      --baseline-secondary:#1A1A1A;
      --baseline-card:#141414;
      --baseline-border:#262626;
    }

    /* Reset and Base Styles */
    * { box-sizing: border-box; margin: 0; padding: 0; }
    body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif; background: var(--baseline-background); color: var(--baseline-foreground); font-size: 16px; line-height: 1.5; }

    /* Utility Classes */
    .min-h-screen { min-height: 100vh; }
    .hidden { display: none !important; }
    .w-full { width: 100%; }
    .h-full { height: 100%; }
    .w-48 { width: 12rem; }
    .h-48 { height: 12rem; }
    .w-6 { width: 1.5rem; }
    .h-6 { height: 1.5rem; }
    .w-32 { width: 8rem; }
    .max-w-6xl { max-width: 72rem; }
    .mx-auto { margin-left: auto; margin-right: auto; }
    .relative { position: relative; }
    .text-center { text-align: center; }
    .text-lg { font-size: 1.125rem; }
    .text-xl { font-size: 1.25rem; }
    .text-2xl { font-size: 1.5rem; }
    .text-4xl { font-size: 2.25rem; }
    .font-bold { font-weight: 700; }
    .font-extrabold { font-weight: 800; }
    .font-semibold { font-weight: 600; }
    .opacity-70 { opacity: 0.7; }
    .opacity-80 { opacity: 0.8; }
    .px-8 { padding-left: 2rem; padding-right: 2rem; }
    .py-12 { padding-top: 3rem; padding-bottom: 3rem; }
    .p-8 { padding: 2rem; }
    .p-6 { padding: 1.5rem; }
    .p-4 { padding: 1rem; }
    .p-3 { padding: 0.75rem; }
    .mb-8 { margin-bottom: 2rem; }
    .mb-6 { margin-bottom: 1.5rem; }
    .mb-4 { margin-bottom: 1rem; }
    .mb-3 { margin-bottom: 0.75rem; }
    .mb-2 { margin-bottom: 0.5rem; }
    .mt-6 { margin-top: 1.5rem; }
    .mt-4 { margin-top: 1rem; }
    .mt-3 { margin-top: 0.75rem; }
    .mt-2 { margin-top: 0.5rem; }
    .pb-20 { padding-bottom: 5rem; }
    .border { border-width: 1px; }
    .border-2 { border-width: 2px; }
    .rounded-lg { border-radius: 0.5rem; }
    .flex { display: flex; }
    .flex-row { flex-direction: row; }
    .flex-1 { flex: 1 1 0%; }
    .justify-center { justify-content: center; }
    .items-center { align-items: center; }
    .gap-4 { gap: 1rem; }
    .gap-3 { gap: 0.75rem; }
    .gap-8 { gap: 2rem; }
    .space-y-6 > * + * { margin-top: 1.5rem; }
    .space-y-4 > * + * { margin-top: 1rem; }
    .flex-wrap { flex-wrap: wrap; }
    .shrink-0 { flex-shrink: 0; }
    .block { display: block; }
    .inline-flex { display: inline-flex; }
    .grid { display: grid; }
    .grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
    .grid-cols-5 { grid-template-columns: repeat(5, minmax(0, 1fr)); }
    .col-span-2 { grid-column: span 2 / span 2; }

    /* Component Styles */
    .btn { min-height: 48px; border-radius: 0.75rem; padding: 1rem 1.5rem; font-weight: 600; font-size: 16px; transition: transform 0.15s ease, opacity 0.15s ease; cursor: pointer; border: none; display: inline-block; text-decoration: none; }
    .btn:active { transform: translateY(1px); opacity: 0.9; }
    .btn-primary { background: var(--baseline-orange); color: var(--baseline-background); }
    .btn-muted { background: var(--baseline-secondary); color: var(--baseline-foreground); border: 1px solid var(--baseline-border); }
    .chip { background: var(--baseline-orange); color: var(--baseline-background); border-radius: 9999px; padding: 0.5rem 0.75rem; font-size: 0.875rem; display: inline-flex; align-items: center; gap: 0.5rem; }
    .card { background: var(--baseline-card); border: 1px solid var(--baseline-border); border-radius: 1rem; box-shadow: 0 8px 24px rgba(0,0,0,0.3); }
    .tab-btn { border: 2px solid var(--baseline-border); border-radius: 0.75rem; padding: 1rem 1.5rem; font-weight: 600; font-size: 16px; background: var(--baseline-secondary); color: var(--baseline-foreground); cursor: pointer; transition: all 0.3s ease; min-width: 180px; text-align: center; }
    .tab-btn.active { background: var(--baseline-orange); border-color: var(--baseline-orange); color: var(--baseline-background); }
    .tab-btn.has-data { border-color: #10b981; }
    .tab-btn.has-data .tab-indicator { color: #10b981; }
    .tab-btn.active.has-data .tab-indicator { color: var(--baseline-background); }
    .tab-indicator { font-size: 12px; margin-right: 8px; color: var(--baseline-border); transition: color 0.3s ease; }

    /* Form Elements */
    input, select, textarea { font-size: 16px; background: var(--baseline-secondary); border: 2px solid var(--baseline-border); color: var(--baseline-foreground); padding: 0.75rem 1rem; border-radius: 0.5rem; }
    input:focus, select:focus, textarea:focus { border-color: var(--baseline-orange); outline: none; box-shadow: 0 0 0 2px rgba(242,108,26,0.2); }

    /* Range Slider */
    .range { -webkit-appearance: none; appearance: none; width: 100%; height: 6px; border-radius: 3px; background: var(--baseline-secondary); outline: none; }
    .range::-webkit-slider-thumb { -webkit-appearance: none; appearance: none; height: 20px; width: 20px; border-radius: 50%; background: var(--baseline-orange); border: 2px solid var(--baseline-foreground); box-shadow: 0 2px 6px rgba(0,0,0,0.4); cursor: pointer; }
    .range::-moz-range-thumb { height: 20px; width: 20px; border-radius: 50%; background: var(--baseline-orange); border: 2px solid var(--baseline-foreground); box-shadow: 0 2px 6px rgba(0,0,0,0.4); cursor: pointer; }

    /* Utility */
    .sr-only { position: absolute; width: 1px; height: 1px; padding: 0; margin: -1px; overflow: hidden; clip: rect(0,0,0,0); white-space: nowrap; border: 0; }
    .notice { position: fixed; top: 1rem; right: 1rem; z-index: 60; transform: translateX(120%); transition: transform 0.25s ease; background: var(--baseline-card); border: 1px solid var(--baseline-border); color: var(--baseline-foreground); padding: 1.5rem; border-radius: 0.5rem; font-weight: 600; font-size: 1.125rem; }
    .notice.show { transform: translateX(0); }
    .fixed { position: fixed; }
    .bottom-8 { bottom: 2rem; }
    .right-8 { right: 2rem; }
    .z-50 { z-index: 50; }
    .shadow-2xl { box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25); }
    .shadow-xl { box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04); }
    .flex { display: flex; }
    .flex-col { flex-direction: column; }
    .gap-3 { gap: 0.75rem; }

    /* Modal Styles */
    .modal-overlay { position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0, 0, 0, 0.8); z-index: 60; display: flex; align-items: center; justify-content: center; }
    .modal-content { background: var(--baseline-background); border: 2px solid var(--baseline-border); border-radius: 1rem; width: 90%; max-width: 800px; max-height: 80vh; overflow: hidden; }
    .modal-header { background: var(--baseline-card); padding: 1.5rem; border-bottom: 1px solid var(--baseline-border); display: flex; justify-content: space-between; align-items: center; }
    .modal-body { padding: 1.5rem; max-height: 60vh; overflow-y: auto; }
    .modal-footer { background: var(--baseline-card); padding: 1.5rem; border-top: 1px solid var(--baseline-border); display: flex; justify-content: space-between; gap: 1rem; }
    .close-btn { background: none; border: none; color: var(--baseline-foreground); font-size: 2rem; cursor: pointer; padding: 0; width: 2rem; height: 2rem; display: flex; align-items: center; justify-content: center; }
    .close-btn:hover { color: var(--baseline-orange); }

    /* Preview Section Styles */
    .preview-section { margin-bottom: 2rem; }
    .preview-section h3 { color: var(--baseline-orange); font-size: 1.25rem; font-weight: 600; margin-bottom: 1rem; display: flex; align-items: center; gap: 0.5rem; }
    .preview-item { background: var(--baseline-card); border: 1px solid var(--baseline-border); border-radius: 0.5rem; padding: 1rem; margin-bottom: 0.75rem; }
    .preview-item-header { font-weight: 600; margin-bottom: 0.5rem; }
    .preview-item-details { color: var(--baseline-foreground); opacity: 0.8; font-size: 0.9rem; }
    .preview-actions { display: flex; gap: 0.5rem; margin-top: 0.75rem; }
    .preview-actions button { padding: 0.25rem 0.75rem; font-size: 0.875rem; }
    .empty-section { color: var(--baseline-foreground); opacity: 0.6; font-style: italic; padding: 1rem; text-align: center; }

    /* Past Dates Interface Styles */
    .checkbox-label { display: flex; align-items: center; gap: 0.5rem; padding: 0.5rem; border: 1px solid var(--baseline-border); border-radius: 0.5rem; background: var(--baseline-card); cursor: pointer; }
    .checkbox-label:hover { background: var(--baseline-background); }
    .checkbox-label input[type="checkbox"] { margin: 0; }
    .space-y-4 > * + * { margin-top: 1rem; }
    .space-y-6 > * + * { margin-top: 1.5rem; }
    .w-32 { width: 8rem; }

    /* Animation */
    .lineAnimation { animation: waveUp 1s ease-in-out infinite; }
    @keyframes waveUp { 0%, 100% { transform: scaleY(0.2); } 50% { transform: scaleY(1); } }
  </style>
</head>
<body class="min-h-screen">
  <!-- Offline banner -->
  <div id="offline" class="hidden w-full text-center text-white bg-red-600 py-2">You're offline. We'll save locally and sync later.</div>

  <header class="px-8 py-12 text-center">
    <!-- Animated Baseline Logo -->
    <div class="w-48 h-48 mx-auto relative mb-6">
      <svg viewBox="0 0 100 100" class="w-full h-full">
        <path
          d="M20,20 L80,20 L80,50 L50,70 L20,50 Z"
          fill="none"
          stroke="#F2D8B3"
          stroke-width="2"
        />
        <line x1="20" y1="20" x2="80" y2="20" stroke="#F2D8B3" stroke-width="2" />
        <text
          x="50"
          y="20"
          text-anchor="middle"
          font-size="9"
          font-weight="bold"
          fill="#0A0A0A"
          stroke-width="4"
          stroke="#0A0A0A"
        >
          BASELINE
        </text>
        <text x="50" y="23" text-anchor="middle" font-size="9" font-weight="bold" fill="#F2D8B3">
          BASELINE
        </text>
        <circle cx="20" cy="20" r="3" fill="#F2D8B3" />
        <circle cx="80" cy="20" r="3" fill="#F2D8B3" />
        <circle cx="20" cy="50" r="3" fill="#F2D8B3" />
        <circle cx="80" cy="50" r="3" fill="#F2D8B3" />
        <!-- Animated lines -->
        <g>
          <line
            x1="30" y1="26" x2="30" y2="51"
            stroke="#F26C1A" stroke-width="2"
            class="lineAnimation"
            style="animation-delay: 0s; transform-origin: 30px 51px"
          />
          <circle cx="30" cy="51" r="2.5" fill="#F26C1A" />
        </g>
        <g>
          <line
            x1="40" y1="32" x2="40" y2="57"
            stroke="#F26C1A" stroke-width="2"
            class="lineAnimation"
            style="animation-delay: 0.2s; transform-origin: 40px 57px"
          />
          <circle cx="40" cy="57" r="2.5" fill="#F26C1A" />
        </g>
        <g>
          <line
            x1="50" y1="36" x2="50" y2="61"
            stroke="#F26C1A" stroke-width="2"
            class="lineAnimation"
            style="animation-delay: 0.4s; transform-origin: 50px 61px"
          />
          <circle cx="50" cy="61" r="2.5" fill="#F26C1A" />
        </g>
        <g>
          <line
            x1="60" y1="32" x2="60" y2="57"
            stroke="#F26C1A" stroke-width="2"
            class="lineAnimation"
            style="animation-delay: 0.6s; transform-origin: 60px 57px"
          />
          <circle cx="60" cy="57" r="2.5" fill="#F26C1A" />
        </g>
        <g>
          <line
            x1="70" y1="26" x2="70" y2="51"
            stroke="#F26C1A" stroke-width="2"
            class="lineAnimation"
            style="animation-delay: 0.8s; transform-origin: 70px 51px"
          />
          <circle cx="70" cy="51" r="2.5" fill="#F26C1A" />
        </g>
        <circle cx="50" cy="70" r="3" fill="#F2D8B3" />
      </svg>
    </div>
    <h2 class="text-4xl font-bold text-[var(--baseline-foreground)] mb-2">Diamond Training Log</h2>
    <p class="text-xl text-[var(--baseline-foreground)] opacity-80">Player Development & Training Portal</p>
  </header>

  <main id="mainInterface" class="max-w-6xl mx-auto px-8 pb-20">
    <!-- Player select -->
    <section class="card p-8 mb-8">
      <h3 class="text-2xl font-extrabold mb-6">Player Selection</h3>
      <label for="player" class="block text-lg font-semibold mb-3">Player Name</label>
      <select id="player" class="w-full border-2 rounded-lg p-4 text-lg" aria-describedby="playerHelp" required>
        <option value="">Select Player</option>
        <option>John Smith</option>
        <option>Sarah Johnson</option>
        <option>Mike Chen</option>
        <option>Emma Wilson</option>
        <option>Alex Rodriguez</option>
        <option>Taylor Davis</option>
      </select>
      <p id="playerHelp" class="text-sm opacity-70 mt-3">Pick your profile before logging anything.</p>
    </section>

    <!-- Tabs -->
    <nav class="card p-6 mb-8">
      <div class="flex flex-row justify-center gap-8 flex-wrap">
        <button class="tab-btn active" data-tab="vision" id="tab-vision-btn">
          <span class="tab-indicator">●</span> Vision
        </button>
        <button class="tab-btn" data-tab="strength" id="tab-strength-btn">
          <span class="tab-indicator">●</span> Strength & Conditioning
        </button>
        <button class="tab-btn" data-tab="training" id="tab-training-btn">
          <span class="tab-indicator">●</span> Training Sessions
        </button>
        <button class="tab-btn" data-tab="recovery" id="tab-recovery-btn">
          <span class="tab-indicator">●</span> Recovery
        </button>
      </div>
    </nav>

    <!-- Tab: Vision -->
    <section id="tab-vision" class="card p-8">
      <h3 class="text-2xl font-extrabold mb-4">Vision Trainer</h3>
      <p class="opacity-80 mb-8 text-lg">Coming soon. You can still mark this section complete to track progress.</p>
      <button class="btn btn-muted save-progress" data-tab="vision">Mark Vision Complete</button>
    </section>

    <!-- Tab: Strength -->
    <section id="tab-strength" class="card p-8 hidden">
      <h3 class="text-2xl font-extrabold mb-6">Strength &amp; Conditioning</h3>

      <!-- Date selector -->
      <div class="mb-8">
        <div class="flex flex-row gap-4">
          <button id="st-today" class="btn btn-primary">Log Today</button>
          <button id="st-past"  class="btn btn-muted">Log Past Days</button>
        </div>
        <div id="st-past-wrap" class="hidden mt-4">
          <input id="st-date" type="date" class="border-2 rounded-lg p-3 text-lg" />
        </div>
        <div id="st-dates" class="flex flex-wrap gap-3 mt-4"></div>
      </div>

      <!-- Lift form -->
      <form id="strengthForm" class="space-y-6">
        <div id="lifts" class="space-y-4"></div>
        <div class="flex flex-row gap-4">
          <button type="button" id="addLift" class="btn btn-muted">Add Lift</button>
        </div>
      </form>
    </section>

    <!-- Tab: Training -->
    <section id="tab-training" class="card p-8 hidden">
      <h3 class="text-2xl font-extrabold mb-6 text-center">Training Sessions</h3>

      <div class="grid grid-cols-2 gap-8">
        <!-- Hitting -->
        <div class="border rounded-lg p-6" style="border-color: var(--baseline-border);">
          <h4 class="text-xl font-bold mb-4">Hitting</h4>
          <div class="flex flex-row gap-3">
            <button class="btn btn-primary" data-ses="hit" data-act="today">Log Today</button>
            <button class="btn btn-muted"  data-ses="hit" data-act="past">Log Past Days</button>
          </div>
          <div class="mt-4 hidden" data-ses="hit" data-wrap="past">
            <input type="date" class="border-2 rounded-lg p-3 text-lg" data-ses="hit" data-input="date" />
          </div>
          <div class="mt-3 flex flex-wrap gap-3" data-ses="hit" data-box="dates"></div>

          <div class="mt-6" data-ses="hit" data-section="current">
            <label class="block text-lg font-semibold mb-2">What you worked on</label>
            <input type="text" class="w-full border-2 rounded-lg p-3 text-lg" data-ses="hit" data-input="focus" placeholder="e.g., Timing on FBs">
          </div>

          <div class="mt-6">
            <label class="block text-lg font-semibold">Success (0–10): <span data-ses="hit" data-out="rating">5</span></label>
            <input type="range" min="0" max="10" value="5" class="w-full range mt-2" data-ses="hit" data-input="rating">
          </div>

          <button class="btn btn-primary mt-6" data-ses="hit" data-act="save">Save Hitting</button>
        </div>

        <!-- Pitching -->
        <div class="border rounded-lg p-6" style="border-color: var(--baseline-border);">
          <h4 class="text-xl font-bold mb-4">Pitching</h4>
          <div class="flex flex-row gap-3">
            <button class="btn btn-primary" data-ses="pit" data-act="today">Log Today</button>
            <button class="btn btn-muted"  data-ses="pit" data-act="past">Log Past Days</button>
          </div>
          <div class="mt-4 hidden" data-ses="pit" data-wrap="past">
            <input type="date" class="border-2 rounded-lg p-3 text-lg" data-ses="pit" data-input="date" />
          </div>
          <div class="mt-3 flex flex-wrap gap-3" data-ses="pit" data-box="dates"></div>

          <div class="mt-6" data-ses="pit" data-section="current">
            <label class="block text-lg font-semibold mb-2">What you worked on</label>
            <input type="text" class="w-full border-2 rounded-lg p-3 text-lg" data-ses="pit" data-input="focus" placeholder="e.g., Curveball command">
          </div>

          <div class="mt-6">
            <label class="block text-lg font-semibold">Success (0–10): <span data-ses="pit" data-out="rating">5</span></label>
            <input type="range" min="0" max="10" value="5" class="w-full range mt-2" data-ses="pit" data-input="rating">
          </div>

          <button class="btn btn-primary mt-6" data-ses="pit" data-act="save">Save Pitching</button>
        </div>
      </div>


    </section>

    <!-- Tab: Recovery -->
    <section id="tab-recovery" class="card p-8 hidden">
      <h3 class="text-2xl font-extrabold mb-6 text-center">Recovery Stations</h3>

      <div class="mb-6 flex flex-row gap-4">
        <button id="rec-today" class="btn btn-primary">Log Today</button>
        <button id="rec-past"  class="btn btn-muted">Log Past Days</button>
      </div>
      <div id="rec-past-wrap" class="hidden mb-4">
        <input id="rec-date" type="date" class="border-2 rounded-lg p-3 text-lg" />
      </div>
      <div id="rec-dates" class="flex flex-wrap gap-3 mb-6"></div>

      <div id="rec-current" class="space-y-4">
        <div class="border rounded-lg p-4 flex items-center justify-between" style="border-color: var(--baseline-border);">
          <label class="text-lg font-semibold">Hyperbolic Chamber</label>
          <div class="flex items-center gap-3">
            <input id="rec-hyp" type="checkbox" class="w-6 h-6">
            <input id="rec-hyp-min" type="number" min="1" max="120" placeholder="mins" class="w-32 border rounded-lg p-3 text-lg hidden">
          </div>
        </div>

        <div class="border rounded-lg p-4 flex items-center justify-between" style="border-color: var(--baseline-border);">
          <label class="text-lg font-semibold">GameReady</label>
          <div class="flex items-center gap-3">
            <input id="rec-gr" type="checkbox" class="w-6 h-6">
            <input id="rec-gr-min" type="number" min="1" max="120" placeholder="mins" class="w-32 border rounded-lg p-3 text-lg hidden">
          </div>
        </div>
      </div>


    </section>
  </main>

  <!-- Submission Preview Modal -->
  <div id="previewModal" class="modal-overlay hidden">
    <div class="modal-content">
      <div class="modal-header">
        <h2 class="text-2xl font-bold">📋 Review Your Session</h2>
        <button id="closeModal" class="close-btn">×</button>
      </div>

      <div id="previewContent" class="modal-body">
        <!-- Preview content will be populated here -->
      </div>

      <div class="modal-footer">
        <button id="editSession" class="btn btn-muted">← Edit Session</button>
        <button id="confirmSubmit" class="btn btn-primary">🎯 Confirm & Submit</button>
      </div>
    </div>
  </div>

  <!-- Past Dates Interface (Hidden by default) -->
  <div id="pastDatesInterface" class="container mx-auto px-6 py-8 hidden">
    <div class="text-center mb-8">
      <h1 class="text-4xl font-bold mb-2">📋 Log Past Dates</h1>
      <p class="text-lg opacity-80">Select dates and assign them to specific activities</p>
    </div>

    <!-- Past Dates Tabs -->
    <div class="flex justify-center mb-8">
      <div class="flex bg-gray-800 rounded-lg p-1">
        <button class="tab-btn active" data-past-tab="strength">💪 Strength</button>
        <button class="tab-btn" data-past-tab="training">🎯 Training</button>
        <button class="tab-btn" data-past-tab="recovery">🔄 Recovery</button>
      </div>
    </div>

    <!-- Past Strength Section -->
    <div id="past-strength" class="tab-content">
      <div class="card">
        <h2 class="text-2xl font-bold mb-6">💪 Past Strength Sessions</h2>

        <!-- Date Picker -->
        <div class="mb-6">
          <label class="block text-lg font-semibold mb-2">Select Dates:</label>
          <input type="date" id="pastStrengthDatePicker" class="border-2 rounded-lg p-3 text-lg mr-3">
          <button id="addStrengthDate" class="btn btn-primary">Add Date</button>
          <div id="selectedStrengthDates" class="mt-3 flex flex-wrap gap-2"></div>
        </div>

        <!-- Lifts with Date Assignment -->
        <div id="pastLifts">
          <!-- Will be populated dynamically -->
        </div>

        <button id="addPastLift" class="btn btn-primary mt-4">+ Add Lift</button>
      </div>
    </div>

    <!-- Past Training Section -->
    <div id="past-training" class="tab-content hidden">
      <div class="card">
        <h2 class="text-2xl font-bold mb-6">🎯 Past Training Sessions</h2>

        <!-- Date Picker -->
        <div class="mb-6">
          <label class="block text-lg font-semibold mb-2">Select Dates:</label>
          <input type="date" id="pastTrainingDatePicker" class="border-2 rounded-lg p-3 text-lg mr-3">
          <button id="addTrainingDate" class="btn btn-primary">Add Date</button>
          <div id="selectedTrainingDates" class="mt-3 flex flex-wrap gap-2"></div>
        </div>

        <!-- Training Sessions with Date Assignment -->
        <div class="space-y-6">
          <!-- Hitting -->
          <div class="border-2 border-gray-700 rounded-lg p-4">
            <h3 class="text-xl font-semibold mb-4">🏏 Hitting</h3>
            <div class="mb-4">
              <label class="block text-lg font-semibold mb-2">What you worked on:</label>
              <input type="text" id="pastHittingFocus" class="w-full border-2 rounded-lg p-3 text-lg" placeholder="e.g., Timing on FBs">
            </div>
            <div class="mb-4">
              <label class="block text-lg font-semibold mb-2">Rating (0-10):</label>
              <input type="range" min="0" max="10" value="5" id="pastHittingRating" class="w-full range">
              <span id="pastHittingRatingValue" class="text-lg font-semibold">5</span>
            </div>
            <div class="mb-4">
              <label class="block text-lg font-semibold mb-2">Apply to dates:</label>
              <div id="hittingDateCheckboxes" class="flex flex-wrap gap-2"></div>
            </div>
          </div>

          <!-- Pitching -->
          <div class="border-2 border-gray-700 rounded-lg p-4">
            <h3 class="text-xl font-semibold mb-4">⚾ Pitching</h3>
            <div class="mb-4">
              <label class="block text-lg font-semibold mb-2">What you worked on:</label>
              <input type="text" id="pastPitchingFocus" class="w-full border-2 rounded-lg p-3 text-lg" placeholder="e.g., Curveball command">
            </div>
            <div class="mb-4">
              <label class="block text-lg font-semibold mb-2">Rating (0-10):</label>
              <input type="range" min="0" max="10" value="5" id="pastPitchingRating" class="w-full range">
              <span id="pastPitchingRatingValue" class="text-lg font-semibold">5</span>
            </div>
            <div class="mb-4">
              <label class="block text-lg font-semibold mb-2">Apply to dates:</label>
              <div id="pitchingDateCheckboxes" class="flex flex-wrap gap-2"></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Past Recovery Section -->
    <div id="past-recovery" class="tab-content hidden">
      <div class="card">
        <h2 class="text-2xl font-bold mb-6">🔄 Past Recovery Sessions</h2>

        <!-- Date Picker -->
        <div class="mb-6">
          <label class="block text-lg font-semibold mb-2">Select Dates:</label>
          <input type="date" id="pastRecoveryDatePicker" class="border-2 rounded-lg p-3 text-lg mr-3">
          <button id="addRecoveryDate" class="btn btn-primary">Add Date</button>
          <div id="selectedRecoveryDates" class="mt-3 flex flex-wrap gap-2"></div>
        </div>

        <!-- Recovery Methods with Date Assignment -->
        <div class="space-y-4">
          <div class="border-2 border-gray-700 rounded-lg p-4">
            <h3 class="text-xl font-semibold mb-4">❄️ Hyperbolic Chamber</h3>
            <div class="mb-4">
              <label class="block text-lg font-semibold mb-2">Minutes:</label>
              <input type="number" id="pastHypMinutes" class="border-2 rounded-lg p-3 text-lg w-32" placeholder="0">
            </div>
            <div class="mb-4">
              <label class="block text-lg font-semibold mb-2">Apply to dates:</label>
              <div id="hypDateCheckboxes" class="flex flex-wrap gap-2"></div>
            </div>
          </div>

          <div class="border-2 border-gray-700 rounded-lg p-4">
            <h3 class="text-xl font-semibold mb-4">🎮 GameReady</h3>
            <div class="mb-4">
              <label class="block text-lg font-semibold mb-2">Minutes:</label>
              <input type="number" id="pastGameReadyMinutes" class="border-2 rounded-lg p-3 text-lg w-32" placeholder="0">
            </div>
            <div class="mb-4">
              <label class="block text-lg font-semibold mb-2">Apply to dates:</label>
              <div id="gameReadyDateCheckboxes" class="flex flex-wrap gap-2"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Mode Switch and Submit Buttons -->
  <div class="fixed bottom-8 right-8 z-50 flex flex-col gap-3">
    <button id="logPastDates" class="btn btn-muted text-lg px-6 py-3 shadow-xl">
      📋 Log Past Dates
    </button>
    <button id="backToToday" class="btn btn-muted text-lg px-6 py-3 shadow-xl hidden">
      📅 Back to Today
    </button>
    <button id="submitAllData" class="btn btn-primary text-xl px-8 py-4 shadow-2xl">
      🎯 Submit Session
    </button>
  </div>

  <!-- Notification -->
  <div id="toast" class="notice px-6 py-4 rounded-lg font-semibold text-lg"></div>

  <script>
    // --- helpers ---
    const $ = (sel,root=document)=>root.querySelector(sel);
    const $$ = (sel,root=document)=>Array.from(root.querySelectorAll(sel));
    const todayStr = ()=> new Date().toISOString().slice(0,10);

    // Google Apps Script Configuration
    const SCRIPT_URL = 'https://script.google.com/macros/s/AKfycbymqLP-tpYisYH8AC-HvyH07XgLSDgwoLpZ_S1JjkDtVmlXQejg-dbM6afUdrdAsQmR/exec';

    function notify(msg, ok=true){
      const toast = $('#toast');
      toast.textContent = msg;
      toast.style.background = ok ? '#10b981' : '#ef4444';
      toast.style.color = '#ffffff';
      toast.classList.add('show');
      setTimeout(()=>toast.classList.remove('show'), 3000);
    }

async function submitToGoogleSheets(data, formType) {
  const SUPABASE_URL = 'https://yozoutklaybngpznifzz.supabase.co';
  const SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inlvem91dGtsYXlibmdwem5pZnp6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTUyNzkwMDEsImV4cCI6MjA3MDg1NTAwMX0._HMmDVWjZYLrqc5Ig7VW5awGu2kPyK5qonmF5pjehF8'; // Truncated for safety

  // Debug print
  console.log("Submitting this training session(s):", data.trainingSessions);

  const response = await fetch(`${SUPABASE_URL}/rest/v1/training_sessions`, {
    method: 'POST',
    headers: {
      'apikey': SUPABASE_KEY,
      'Authorization': `Bearer ${SUPABASE_KEY}`,
      'Content-Type': 'application/json',
      'Prefer': 'return=minimal'
    },
    body: JSON.stringify(data.trainingSessions) // <-- FIXED HERE
  });

  return response.ok;
}

    function requirePlayer(){
      const val = $('#player').value.trim();
      if(!val){ notify('Please select a player first.', false); $('#player').focus(); return null; }
      return val;
    }

    // Modal Preview Functions
    function showPreviewModal() {
      const player = requirePlayer();
      if (!player) return;

      const sessionData = collectSessionData(player);
      if (!sessionData) return;

      populatePreview(sessionData);
      $('#previewModal').classList.remove('hidden');
    }

    function collectSessionData(player) {
      const sessionData = {
        player: player,
        timestamp: new Date().toISOString(),
        sections: {
          strength: [],
          training: [],
          recovery: []
        }
      };

      // Collect data based on current mode
      if (currentMode === 'today') {
        console.log('=== COLLECTING TODAY\'S DATA ===');
        collectTodayData(sessionData);
      } else if (currentMode === 'past') {
        console.log('=== COLLECTING PAST DATES DATA ===');
        collectPastDatesData(sessionData);
      } else {
        // Fallback: collect both if mode is unclear
        console.log('=== MODE UNCLEAR - COLLECTING BOTH ===');
        collectTodayData(sessionData);
        collectPastDatesData(sessionData);
      }

      // Check if any data was collected
      const hasData = sessionData.sections.strength.length > 0 ||
                     sessionData.sections.training.length > 0 ||
                     sessionData.sections.recovery.length > 0;

      if (!hasData) {
        notify('Please complete at least one section before submitting!', false);
        return null;
      }

      return sessionData;
    }

    function collectTodayData(sessionData) {
      const today = new Date().toISOString().split('T')[0];

      // Collect today's strength data
      $$('#lifts > div').forEach(row => {
        const [name, sets, reps, weight] = row.querySelectorAll('input');
        if (name.value.trim() && weight.value) {
          const maxBtn = row.querySelector('button[data-max]');
          sessionData.sections.strength.push({
            name: name.value.trim(),
            sets: sets.value ? +sets.value : null,
            reps: reps.value ? +reps.value : null,
            weight: +weight.value,
            isMax: maxBtn.dataset.max === '1',
            date: today,
            isToday: true
          });
        }
      });

      // Collect today's training data
      const hitFocus = $('[data-ses="hit"][data-input="focus"]')?.value?.trim();
      const hitRating = $('[data-ses="hit"][data-input="rating"]')?.value;
      if (hitFocus || hitRating) {
        sessionData.sections.training.push({
          type: 'hitting',
          focus: hitFocus || '',
          rating: hitRating ? +hitRating : null,
          date: today,
          isToday: true
        });
      }

      const pitFocus = $('[data-ses="pit"][data-input="focus"]')?.value?.trim();
      const pitRating = $('[data-ses="pit"][data-input="rating"]')?.value;
      if (pitFocus || pitRating) {
        sessionData.sections.training.push({
          type: 'pitching',
          focus: pitFocus || '',
          rating: pitRating ? +pitRating : null,
          date: today,
          isToday: true
        });
      }

      // Collect today's recovery data
      if ($('#rec-hyp').checked) {
        const mins = parseInt($('#rec-hyp-min').value || '0', 10);
        if (mins > 0) {
          sessionData.sections.recovery.push({
            method: 'Hyperbolic Chamber',
            minutes: mins,
            date: today,
            isToday: true
          });
        }
      }
      if ($('#rec-gr').checked) {
        const mins = parseInt($('#rec-gr-min').value || '0', 10);
        if (mins > 0) {
          sessionData.sections.recovery.push({
            method: 'GameReady',
            minutes: mins,
            date: today,
            isToday: true
          });
        }
      }
    }

    function collectPastDatesData(sessionData) {
      console.log('=== COLLECTING PAST DATES DATA ===');
      console.log('pastDatesData object:', pastDatesData);

      // Collect past strength data
      console.log('Past strength lifts:', pastDatesData.strength.lifts);
      pastDatesData.strength.lifts.forEach((lift, index) => {
        console.log(`Lift ${index}:`, lift);
        if (lift.name && lift.weight && lift.dates.length > 0) {
          lift.dates.forEach(date => {
            console.log(`Adding strength entry for date: ${date}`);
            sessionData.sections.strength.push({
              name: lift.name,
              sets: lift.sets ? +lift.sets : null,
              reps: lift.reps ? +lift.reps : null,
              weight: +lift.weight,
              isMax: lift.isMax,
              date: date,
              isToday: false
            });
          });
        }
      });

      // Collect past training data
      console.log('Past hitting data:', pastDatesData.training.hitting);
      if (pastDatesData.training.hitting.focus && pastDatesData.training.hitting.dates.length > 0) {
        console.log('Adding hitting entries for dates:', pastDatesData.training.hitting.dates);
        pastDatesData.training.hitting.dates.forEach(date => {
          console.log(`Adding hitting entry for date: ${date}`);
          sessionData.sections.training.push({
            type: 'hitting',
            focus: pastDatesData.training.hitting.focus,
            rating: pastDatesData.training.hitting.rating,
            date: date,
            isToday: false
          });
        });
      }

      console.log('Past pitching data:', pastDatesData.training.pitching);
      if (pastDatesData.training.pitching.focus && pastDatesData.training.pitching.dates.length > 0) {
        console.log('Adding pitching entries for dates:', pastDatesData.training.pitching.dates);
        pastDatesData.training.pitching.dates.forEach(date => {
          console.log(`Adding pitching entry for date: ${date}`);
          sessionData.sections.training.push({
            type: 'pitching',
            focus: pastDatesData.training.pitching.focus,
            rating: pastDatesData.training.pitching.rating,
            date: date,
            isToday: false
          });
        });
      }

      // Collect past recovery data
      console.log('Past recovery data:', pastDatesData.recovery);
      if (pastDatesData.recovery.hyperbolic.minutes > 0 && pastDatesData.recovery.hyperbolic.dates.length > 0) {
        pastDatesData.recovery.hyperbolic.dates.forEach(date => {
          console.log(`Adding hyperbolic recovery entry for date: ${date}`);
          sessionData.sections.recovery.push({
            method: 'Hyperbolic Chamber',
            minutes: pastDatesData.recovery.hyperbolic.minutes,
            date: date,
            isToday: false
          });
        });
      }

      if (pastDatesData.recovery.gameready.minutes > 0 && pastDatesData.recovery.gameready.dates.length > 0) {
        pastDatesData.recovery.gameready.dates.forEach(date => {
          console.log(`Adding GameReady recovery entry for date: ${date}`);
          sessionData.sections.recovery.push({
            method: 'GameReady',
            minutes: pastDatesData.recovery.gameready.minutes,
            date: date,
            isToday: false
          });
        });
      }

      console.log('=== FINAL SESSION DATA ===');
      console.log('sessionData.sections:', sessionData.sections);
    }

    function populatePreview(sessionData) {
      const previewContent = $('#previewContent');
      previewContent.innerHTML = '';

      // Player info header
      const playerHeader = document.createElement('div');
      playerHeader.className = 'preview-section';
      playerHeader.innerHTML = `
        <h3>👤 Player: ${sessionData.player}</h3>
        <div class="preview-item">
          <div class="preview-item-details">Submission Date: ${new Date().toLocaleDateString()}</div>
        </div>
      `;
      previewContent.appendChild(playerHeader);

      // Organize data by date
      const dataByDate = organizeDataByDate(sessionData);

      // Display today's sessions first
      if (dataByDate.today && Object.keys(dataByDate.today).length > 0) {
        createDateSection(previewContent, 'today', new Date().toISOString().split('T')[0], dataByDate.today);
      }

      // Display past sessions organized by date
      Object.keys(dataByDate.past).sort().forEach(date => {
        createDateSection(previewContent, 'past', date, dataByDate.past[date]);
      });

      // Show message if no data
      if (!dataByDate.today || Object.keys(dataByDate.today).length === 0) {
        if (Object.keys(dataByDate.past).length === 0) {
          const emptySection = document.createElement('div');
          emptySection.className = 'preview-section';
          emptySection.innerHTML = '<div class="empty-section">No session data to submit</div>';
          previewContent.appendChild(emptySection);
        }
      }
    }

    function organizeDataByDate(sessionData) {
      const today = new Date().toISOString().split('T')[0];
      const organized = {
        today: { strength: [], training: [], recovery: [] },
        past: {}
      };

      // Organize strength data
      sessionData.sections.strength.forEach(lift => {
        if (lift.isToday || lift.date === today) {
          organized.today.strength.push(lift);
        } else {
          if (!organized.past[lift.date]) {
            organized.past[lift.date] = { strength: [], training: [], recovery: [] };
          }
          organized.past[lift.date].strength.push(lift);
        }
      });

      // Organize training data
      sessionData.sections.training.forEach(training => {
        if (training.isToday || training.date === today) {
          organized.today.training.push(training);
        } else {
          if (!organized.past[training.date]) {
            organized.past[training.date] = { strength: [], training: [], recovery: [] };
          }
          organized.past[training.date].training.push(training);
        }
      });

      // Organize recovery data
      sessionData.sections.recovery.forEach(recovery => {
        if (recovery.isToday || recovery.date === today) {
          organized.today.recovery.push(recovery);
        } else {
          if (!organized.past[recovery.date]) {
            organized.past[recovery.date] = { strength: [], training: [], recovery: [] };
          }
          organized.past[recovery.date].recovery.push(recovery);
        }
      });

      return organized;
    }

    function createDateSection(container, type, date, data) {
      const hasData = data.strength.length > 0 || data.training.length > 0 || data.recovery.length > 0;
      if (!hasData) return;

      const dateSection = document.createElement('div');
      dateSection.className = 'preview-section';

      const dateTitle = type === 'today' ?
        `📅 Today's Sessions (${new Date(date).toLocaleDateString()})` :
        `📅 Past Session (${new Date(date).toLocaleDateString()})`;

      dateSection.innerHTML = `<h3>${dateTitle}</h3>`;

      // Add strength items for this date
      data.strength.forEach((lift, index) => {
        const item = document.createElement('div');
        item.className = 'preview-item';
        item.innerHTML = `
          <div class="preview-item-header">💪 ${lift.name}</div>
          <div class="preview-item-details">
            ${lift.sets ? lift.sets + ' sets' : ''}
            ${lift.reps ? ' × ' + lift.reps + ' reps' : ''}
            @ ${lift.weight} lbs
            ${lift.isMax ? '(MAX)' : ''}
          </div>
        `;
        dateSection.appendChild(item);
      });

      // Add training items for this date
      data.training.forEach((training, index) => {
        const item = document.createElement('div');
        item.className = 'preview-item';
        item.innerHTML = `
          <div class="preview-item-header">🎯 ${training.type === 'hitting' ? 'Hitting' : 'Pitching'}</div>
          <div class="preview-item-details">
            Working on: ${training.focus || 'Not specified'}<br>
            Rating: ${training.rating || 'Not rated'}
          </div>
        `;
        dateSection.appendChild(item);
      });

      // Add recovery items for this date
      data.recovery.forEach((recovery, index) => {
        const item = document.createElement('div');
        item.className = 'preview-item';
        item.innerHTML = `
          <div class="preview-item-header">🔄 ${recovery.method}</div>
          <div class="preview-item-details">${recovery.minutes} minutes</div>
        `;
        dateSection.appendChild(item);
      });

      container.appendChild(dateSection);
    }

    // Modal event handlers
    $('#submitAllData').addEventListener('click', showPreviewModal);
    $('#closeModal').addEventListener('click', () => $('#previewModal').classList.add('hidden'));
    $('#editSession').addEventListener('click', () => $('#previewModal').classList.add('hidden'));

    $('#confirmSubmit').addEventListener('click', async () => {
      const player = requirePlayer();
      if (!player) return;

      const sessionData = collectSessionData(player);
      if (!sessionData) return;

      $('#previewModal').classList.add('hidden');
      notify('Submitting session...');

      try {
        console.log('=== PREPARING GOOGLE SHEETS DATA ===');
        console.log('Raw sessionData before conversion:', sessionData);

        // Convert sessionData to the format expected by Google Apps Script
        const googleSheetsData = {
          player: sessionData.player,
          timestamp: sessionData.timestamp,
          workouts: JSON.stringify(sessionData.sections.strength.map(lift => ({
            player: sessionData.player,
            type: 'strength',
            date: lift.date, // Use the actual lift date, not today's date
            lifts: [lift],
            savedAt: sessionData.timestamp
          }))),
          trainingSessions: JSON.stringify(sessionData.sections.training.map(training => ({
            player: sessionData.player,
            timestamp: sessionData.timestamp,
            hitting: training.type === 'hitting' ? {
              focus: training.focus,
              rating: training.rating,
              date: training.date, // This should be the actual training date
              isToday: training.isToday
            } : null,
            pitching: training.type === 'pitching' ? {
              focus: training.focus,
              rating: training.rating,
              date: training.date, // This should be the actual training date
              isToday: training.isToday
            } : null,
            type: 'training'
          }))),
          recoverySessions: JSON.stringify(sessionData.sections.recovery.map(recovery => ({
            player: sessionData.player,
            date: recovery.date, // Use the actual recovery date, not today's date
            items: [recovery],
            savedAt: sessionData.timestamp,
            type: 'recovery'
          }))),
          progressData: '{}'
        };

        console.log('=== GOOGLE SHEETS DATA ===');
        console.log('googleSheetsData:', googleSheetsData);
        console.log('Training sessions JSON:', JSON.parse(googleSheetsData.trainingSessions));

        const success = await submitToGoogleSheets(googleSheetsData, 'allData');
        if (success) {
          notify('Session submitted successfully! 🎉');

          // Refresh the page after a short delay
          setTimeout(() => {
            window.location.reload();
          }, 1500);

        } else {
          notify('Session saved locally. Please check Google Sheets to verify sync.', true);
        }
      } catch (error) {
        console.error('Error submitting session:', error);
        notify('Error submitting session. Please try again.', false);
      }
    });

    // Edit functions (close modal and switch to appropriate tab)
    function editStrengthFromPreview(index) {
      $('#previewModal').classList.add('hidden');
      switchTab('strength');
    }

    function removeStrengthFromPreview(index) {
      // Remove the lift row from the form
      const liftRows = $$('#lifts > div');
      if (liftRows[index]) {
        liftRows[index].remove();
      }
      // Refresh the preview
      const player = requirePlayer();
      if (player) {
        const sessionData = collectSessionData(player);
        if (sessionData) {
          populatePreview(sessionData);
        }
      }
      updateTabIndicators();
    }

    function editTrainingFromPreview(type) {
      $('#previewModal').classList.add('hidden');
      switchTab('training');
    }

    function removeTrainingFromPreview(index) {
      // For individual entry removal, we need to clear the entire training type
      // since we can't easily remove individual dates from the form
      const player = requirePlayer();
      if (!player) return;

      const sessionData = collectSessionData(player);
      if (!sessionData || !sessionData.sections.training[index]) return;

      const trainingToRemove = sessionData.sections.training[index];
      const type = trainingToRemove.type;

      // Clear all data for this training type
      if (type === 'hitting') {
        $('[data-ses="hit"][data-input="focus"]').value = '';
        $('[data-ses="hit"][data-input="rating"]').value = '';
        // Reset to today mode and clear past dates
        if (window.trainingStates?.hit) {
          window.trainingStates.hit.today = true;
          window.trainingStates.hit.dates = [];
        }
      } else if (type === 'pitching') {
        $('[data-ses="pit"][data-input="focus"]').value = '';
        $('[data-ses="pit"][data-input="rating"]').value = '';
        // Reset to today mode and clear past dates
        if (window.trainingStates?.pit) {
          window.trainingStates.pit.today = true;
          window.trainingStates.pit.dates = [];
        }
      }

      // Refresh the preview
      const newSessionData = collectSessionData(player);
      if (newSessionData) {
        populatePreview(newSessionData);
      }
      updateTabIndicators();
    }

    function editRecoveryFromPreview(index) {
      $('#previewModal').classList.add('hidden');
      switchTab('recovery');
    }

    function removeRecoveryFromPreview(index) {
      // Clear recovery checkboxes and inputs
      $('#rec-hyp').checked = false;
      $('#rec-gr').checked = false;
      $('#rec-hyp-min').value = '';
      $('#rec-gr-min').value = '';
      $('#rec-hyp-min').classList.add('hidden');
      $('#rec-gr-min').classList.add('hidden');
      // Refresh the preview
      const player = requirePlayer();
      if (player) {
        const sessionData = collectSessionData(player);
        if (sessionData) {
          populatePreview(sessionData);
        }
      }
      updateTabIndicators();
    }



    // --- offline banner ---
    function setOfflineBanner(){ $('#offline').classList.toggle('hidden', navigator.onLine); }
    window.addEventListener('online', setOfflineBanner);
    window.addEventListener('offline', setOfflineBanner);
    setOfflineBanner();

    // --- player select persist ---
    (function initPlayer(){
      const saved = localStorage.getItem('selectedPlayer');
      if(saved) $('#player').value = saved;
      $('#player').addEventListener('change', e=>{
        const v = e.target.value || '';
        if(v) localStorage.setItem('selectedPlayer', v);
        else localStorage.removeItem('selectedPlayer');
      });
    })();

    // --- tabs ---
    (function initTabs(){
      const btns = $$('.tab-btn');
      btns.forEach(b=>b.addEventListener('click', ()=>{
        updateTabIndicators(); // Update indicators when switching tabs
        switchTab(b.dataset.tab);
      }));
      switchTab('vision'); // default

      function switchTab(name){
        btns.forEach(b=>b.classList.toggle('active', b.dataset.tab===name));
        ['vision','strength','training','recovery'].forEach(t=>{
          $('#tab-'+t).classList.toggle('hidden', t!==name);
        });
      }

      // Update tab indicators based on form data
      window.updateTabIndicators = function() {
        // Check strength section
        const hasStrengthData = $$('#lifts > div').some(row => {
          const [name, , , weight] = row.querySelectorAll('input');
          return name.value.trim() && weight.value;
        });
        $('#tab-strength-btn').classList.toggle('has-data', hasStrengthData);

        // Check training section - check all training inputs AND past dates
        let hasTrainingData = false;

        // Check all training focus inputs
        const trainingInputs = document.querySelectorAll('[data-ses][data-input="focus"]');
        trainingInputs.forEach(input => {
          const sesType = input.getAttribute('data-ses');
          const focusValue = input.value && input.value.trim();
          const state = window.trainingStates?.[sesType];

          // Has data if there's focus text OR if there are past dates selected
          if (focusValue || (state && !state.today && state.dates.length > 0)) {
            hasTrainingData = true;
            console.log('Found training data:', sesType, 'focus:', focusValue, 'pastDates:', state?.dates);
          }
        });

        console.log('Training check result:', hasTrainingData);
        $('#tab-training-btn').classList.toggle('has-data', hasTrainingData);

        // Check recovery section
        const hasRecoveryData = $('#rec-hyp').checked || $('#rec-gr').checked;
        $('#tab-recovery-btn').classList.toggle('has-data', hasRecoveryData);

        // Vision section - no inputs available yet, so never green
        $('#tab-vision-btn').classList.remove('has-data');
      };

      // Add event listeners to form inputs to update indicators in real-time
      document.addEventListener('input', updateTabIndicators);
      document.addEventListener('change', updateTabIndicators);

      // Initial check
      setTimeout(updateTabIndicators, 100);
    })();

    // --- simple "Save Progress (Draft)" for each tab ---
    $$('.save-progress').forEach(btn=>{
      btn.addEventListener('click', ()=>{
        const player = requirePlayer(); if(!player) return;
        const tab = btn.dataset.tab;
        const key = 'draft_'+tab+'_'+player;
        localStorage.setItem(key, JSON.stringify({player, tab, savedAt:new Date().toISOString()}));
        notify(tab.charAt(0).toUpperCase()+tab.slice(1)+' progress saved.');
      });
    });

    // --- Strength tab ---
    (function initStrength(){
      const st = { today:true, dates:[] };
      const dateInput = $('#st-date');
      const wrap = $('#st-past-wrap');
      const datesBox = $('#st-dates');

      function setMinMax(input){
        const t = todayStr();
        input.max = t;
        const d = new Date(); d.setMonth(d.getMonth()-2);
        input.min = d.toISOString().slice(0,10);
      }
      setMinMax(dateInput);

      $('#st-today').addEventListener('click',()=>{st.today=true; wrap.classList.add('hidden'); st.dates=[]; renderDates();});
      $('#st-past').addEventListener('click',()=>{st.today=false; wrap.classList.remove('hidden');});
      dateInput.addEventListener('change', e=>{
        const v = e.target.value;
        if(v && !st.dates.includes(v)){ st.dates.push(v); renderDates(); e.target.value=''; }
      });

      function renderDates(){
        datesBox.innerHTML = '';
        st.dates.forEach(d=>{
          const div = document.createElement('div');
          div.className = 'chip'; div.innerHTML = `${new Date(d).toLocaleDateString()} <button aria-label="remove date">✕</button>`;
          div.querySelector('button').onclick = ()=>{ st.dates = st.dates.filter(x=>x!==d); renderDates(); };
          datesBox.appendChild(div);
        });
      }

      // lifts
      const liftsBox = $('#lifts');
      $('#addLift').addEventListener('click', addLift);
      function addLift(){
        const row = document.createElement('div');
        row.className = 'grid grid-cols-5 gap-4 border rounded-lg p-4';
        row.style.borderColor = 'var(--baseline-border)';
        row.innerHTML = `
          <div class="col-span-2">
            <label class="block text-lg font-semibold mb-2">Lift Name *</label>
            <input type="text" required class="w-full border-2 rounded-lg p-3 text-lg capitalize" placeholder="Bench Press">
          </div>
          <div>
            <label class="block text-lg font-semibold mb-2">Sets</label>
            <input type="number" min="1" class="w-full border-2 rounded-lg p-3 text-lg" placeholder="3">
          </div>
          <div>
            <label class="block text-lg font-semibold mb-2">Reps</label>
            <input type="number" min="1" class="w-full border-2 rounded-lg p-3 text-lg" placeholder="10">
          </div>
          <div>
            <label class="block text-lg font-semibold mb-2">Weight (lbs) *</label>
            <div class="flex gap-3">
              <input type="number" step="0.5" min="0" required class="w-full border-2 rounded-lg p-3 text-lg" placeholder="135">
              <button type="button" class="btn btn-muted shrink-0" data-max="0">MAX</button>
              <button type="button" class="btn btn-muted shrink-0" aria-label="remove">✕</button>
            </div>
          </div>`;
        const [name, sets, reps, weight] = row.querySelectorAll('input');
        const [maxBtn, removeBtn] = row.querySelectorAll('button');
        maxBtn.onclick = ()=>{
          const on = maxBtn.dataset.max === '1';
          maxBtn.dataset.max = on ? '0' : '1';
          maxBtn.classList.toggle('bg-rose-600', !on);
          maxBtn.classList.toggle('text-white', !on);
        };
        removeBtn.onclick = ()=> {
          row.remove();
          updateTabIndicators();
        };
        liftsBox.appendChild(row);

        // Add event listeners to update indicators when data changes
        [name, sets, reps, weight].forEach(input => {
          input.addEventListener('input', updateTabIndicators);
        });
      }
      // initial lift row
      addLift();


    })();

    // --- Training (hitting/pitching) ---
    // Global training states for data collection
    window.trainingStates = {
      hit: { today: true, dates: [] },
      pit: { today: true, dates: [] }
    };

    (function initTraining(){
      function setup(kind){
        const state = window.trainingStates[kind];
        const wrapPast = $(`[data-ses="${kind}"][data-wrap="past"]`);
        const inputDate = $(`[data-ses="${kind}"][data-input="date"]`);
        const datesBox  = $(`[data-ses="${kind}"][data-box="dates"]`);
        const current   = $(`[data-ses="${kind}"][data-section="current"]`);
        const rating    = $(`[data-ses="${kind}"][data-input="rating"]`);
        const ratingOut = $(`[data-ses="${kind}"][data-out="rating"]`);
        const focus     = $(`[data-ses="${kind}"][data-input="focus"]`);

        // min/max
        inputDate.max = todayStr();
        const d = new Date(); d.setMonth(d.getMonth()-2); inputDate.min = d.toISOString().slice(0,10);

        // controls
        $$('[data-ses="'+kind+'"][data-act]').forEach(btn=>{
          btn.addEventListener('click', ()=>{
            const act = btn.dataset.act;
            if(act==='today'){
              state.today = true;
              state.dates=[];
              wrapPast.classList.add('hidden');
              current.classList.remove('hidden');
              renderDates();
              updateTabIndicators(); // Update indicators when switching to today
            }
            if(act==='past'){
              state.today = false;
              wrapPast.classList.remove('hidden');
              current.classList.add('hidden');
              updateTabIndicators(); // Update indicators when switching to past
            }
            if(act==='save'){  saveOne(); }
          });
        });

        inputDate.addEventListener('change', e=>{
          const v = e.target.value;
          if(v && !state.dates.includes(v)){
            state.dates.push(v);
            renderDates();
            e.target.value='';
            updateTabIndicators(); // Update indicators when dates are added
          }
        });
        rating.addEventListener('input', ()=> ratingOut.textContent = rating.value);

        // Add event listener to focus input to update tab indicators
        focus.addEventListener('input', () => {
          console.log(`${kind} focus changed:`, focus.value);
          updateTabIndicators();
        });

        function renderDates(){
          datesBox.innerHTML='';
          state.dates.forEach(d=>{
            const div=document.createElement('div');
            div.className='chip'; div.innerHTML=`${new Date(d).toLocaleDateString()} <button>✕</button>`;
            div.querySelector('button').onclick=()=>{state.dates=state.dates.filter(x=>x!==d); renderDates();};
            datesBox.appendChild(div);
          });
        }

        function getPayload(){
          const hasRating = !!rating.value;
          const hasDate = state.today || state.dates.length;
          if(!hasRating || !hasDate){ notify('Complete the '+(kind==='hit'?'Hitting':'Pitching')+' form.', false); return null; }
          const p = { rating:+rating.value, date: state.today ? todayStr() : [...state.dates], isToday: state.today };
          if(state.today && focus && focus.value.trim()) p.focus = focus.value.trim();
          return p;
        }

        function saveOne(){
          const player = requirePlayer(); if(!player) return;
          const payload = getPayload(); if(!payload) return;
          const key = 'trainingSessions';
          const all = JSON.parse(localStorage.getItem(key)||'[]');
          all.push({ player, timestamp:new Date().toISOString(), [kind==='hit'?'hitting':'pitching']: payload });
          localStorage.setItem(key, JSON.stringify(all));
          notify((kind==='hit'?'Hitting':'Pitching')+' session saved.');
          // reset
          if(focus) focus.value=''; rating.value='5'; ratingOut.textContent='5'; state.today=true; state.dates=[]; wrapPast.classList.add('hidden'); current.classList.remove('hidden'); renderDates();
        }

        return { getPayload, saveOne };
      }

      const hit = setup('hit');
      const pit = setup('pit');


    })();

    // --- Recovery ---
    (function initRecovery(){
      const rec = { today:true, dates:[] };
      const wrap = $('#rec-past-wrap');
      const dateInput = $('#rec-date');
      const datesBox = $('#rec-dates');
      dateInput.max = todayStr(); const d=new Date(); d.setMonth(d.getMonth()-2); dateInput.min = d.toISOString().slice(0,10);

      $('#rec-today').addEventListener('click',()=>{ rec.today=true; rec.dates=[]; wrap.classList.add('hidden'); renderDates(); });
      $('#rec-past').addEventListener('click',()=>{ rec.today=false; wrap.classList.remove('hidden'); });
      dateInput.addEventListener('change', e=>{
        const v=e.target.value; if(v && !rec.dates.includes(v)){ rec.dates.push(v); renderDates(); e.target.value=''; }
      });

      function renderDates(){
        datesBox.innerHTML='';
        rec.dates.forEach(x=>{
          const el=document.createElement('div');
          el.className='chip'; el.innerHTML=`${new Date(x).toLocaleDateString()} <button>✕</button>`;
          el.querySelector('button').onclick=()=>{ rec.dates=rec.dates.filter(d=>d!==x); renderDates(); };
          datesBox.appendChild(el);
        });
      }

      // toggles
      $('#rec-hyp').addEventListener('change', e=> $('#rec-hyp-min').classList.toggle('hidden', !e.target.checked));
      $('#rec-gr').addEventListener('change', e=> $('#rec-gr-min').classList.toggle('hidden', !e.target.checked));


    })();

    // --- Mode Switching (Today vs Past Dates) ---
    let currentMode = 'today'; // 'today' or 'past'

    const todayInterface = $('#mainInterface');
    const pastDatesInterface = $('#pastDatesInterface');
    const logPastDatesBtn = $('#logPastDates');
    const backToTodayBtn = $('#backToToday');

    // Mode switching functions
    function switchToTodayMode() {
      currentMode = 'today';
      todayInterface.classList.remove('hidden');
      pastDatesInterface.classList.add('hidden');
      logPastDatesBtn.classList.remove('hidden');
      backToTodayBtn.classList.add('hidden');
    }

    function switchToPastMode() {
      currentMode = 'past';
      todayInterface.classList.add('hidden');
      pastDatesInterface.classList.remove('hidden');
      logPastDatesBtn.classList.add('hidden');
      backToTodayBtn.classList.remove('hidden');
    }

    // Event listeners for mode switching
    logPastDatesBtn.addEventListener('click', switchToPastMode);
    backToTodayBtn.addEventListener('click', switchToTodayMode);

    // --- Past Dates Tab Switching ---
    $$('[data-past-tab]').forEach(btn => {
      btn.addEventListener('click', () => {
        const targetTab = btn.dataset.pastTab;

        // Update active tab button
        $$('[data-past-tab]').forEach(b => b.classList.remove('active'));
        btn.classList.add('active');

        // Show target tab content
        $$('#pastDatesInterface .tab-content').forEach(content => {
          content.classList.add('hidden');
        });
        $(`#past-${targetTab}`).classList.remove('hidden');
      });
    });

    // --- Past Dates Data Management ---
    const pastDatesData = {
      strength: {
        selectedDates: [],
        lifts: []
      },
      training: {
        selectedDates: [],
        hitting: { focus: '', rating: 5, dates: [] },
        pitching: { focus: '', rating: 5, dates: [] }
      },
      recovery: {
        selectedDates: [],
        hyperbolic: { minutes: 0, dates: [] },
        gameready: { minutes: 0, dates: [] }
      }
    };

    // Date picker functionality
    function setupDatePicker(section) {
      const datePicker = $(`#past${section.charAt(0).toUpperCase() + section.slice(1)}DatePicker`);
      const addBtn = $(`#add${section.charAt(0).toUpperCase() + section.slice(1)}Date`);
      const datesContainer = $(`#selected${section.charAt(0).toUpperCase() + section.slice(1)}Dates`);

      addBtn.addEventListener('click', () => {
        const selectedDate = datePicker.value;
        if (selectedDate && !pastDatesData[section].selectedDates.includes(selectedDate)) {
          pastDatesData[section].selectedDates.push(selectedDate);
          renderSelectedDates(section);
          updateDateCheckboxes(section);
          datePicker.value = '';
        }
      });
    }

    function renderSelectedDates(section) {
      const container = $(`#selected${section.charAt(0).toUpperCase() + section.slice(1)}Dates`);
      container.innerHTML = '';

      pastDatesData[section].selectedDates.forEach(date => {
        const chip = document.createElement('div');
        chip.className = 'chip';
        chip.innerHTML = `${new Date(date).toLocaleDateString()} <button onclick="removePastDate('${section}', '${date}')">✕</button>`;
        container.appendChild(chip);
      });
    }

    function removePastDate(section, date) {
      pastDatesData[section].selectedDates = pastDatesData[section].selectedDates.filter(d => d !== date);
      renderSelectedDates(section);
      updateDateCheckboxes(section);
    }

    function updateDateCheckboxes(section) {
      if (section === 'training') {
        updateTrainingDateCheckboxes();
      } else if (section === 'recovery') {
        updateRecoveryDateCheckboxes();
      }
      // Strength will be handled when lifts are added
    }

    function updateTrainingDateCheckboxes() {
      const dates = pastDatesData.training.selectedDates;

      // Update hitting checkboxes
      const hittingContainer = $('#hittingDateCheckboxes');
      hittingContainer.innerHTML = '';
      dates.forEach(date => {
        const label = document.createElement('label');
        label.className = 'checkbox-label';
        label.innerHTML = `
          <input type="checkbox" value="${date}" onchange="updateTrainingDateSelection('hitting', '${date}', this.checked)">
          ${new Date(date).toLocaleDateString()}
        `;
        hittingContainer.appendChild(label);
      });

      // Update pitching checkboxes
      const pitchingContainer = $('#pitchingDateCheckboxes');
      pitchingContainer.innerHTML = '';
      dates.forEach(date => {
        const label = document.createElement('label');
        label.className = 'checkbox-label';
        label.innerHTML = `
          <input type="checkbox" value="${date}" onchange="updateTrainingDateSelection('pitching', '${date}', this.checked)">
          ${new Date(date).toLocaleDateString()}
        `;
        pitchingContainer.appendChild(label);
      });
    }

    function updateTrainingDateSelection(type, date, checked) {
      console.log(`=== TRAINING DATE SELECTION ===`);
      console.log(`Type: ${type}, Date: ${date}, Checked: ${checked}`);
      console.log('Before update:', pastDatesData.training[type].dates);

      if (checked) {
        if (!pastDatesData.training[type].dates.includes(date)) {
          pastDatesData.training[type].dates.push(date);
        }
      } else {
        pastDatesData.training[type].dates = pastDatesData.training[type].dates.filter(d => d !== date);
      }

      console.log('After update:', pastDatesData.training[type].dates);
      console.log('Full pastDatesData.training:', pastDatesData.training);
    }

    function updateRecoveryDateCheckboxes() {
      const dates = pastDatesData.recovery.selectedDates;

      // Update hyperbolic checkboxes
      const hypContainer = $('#hypDateCheckboxes');
      hypContainer.innerHTML = '';
      dates.forEach(date => {
        const label = document.createElement('label');
        label.className = 'checkbox-label';
        label.innerHTML = `
          <input type="checkbox" value="${date}" onchange="updateRecoveryDateSelection('hyperbolic', '${date}', this.checked)">
          ${new Date(date).toLocaleDateString()}
        `;
        hypContainer.appendChild(label);
      });

      // Update gameready checkboxes
      const gameReadyContainer = $('#gameReadyDateCheckboxes');
      gameReadyContainer.innerHTML = '';
      dates.forEach(date => {
        const label = document.createElement('label');
        label.className = 'checkbox-label';
        label.innerHTML = `
          <input type="checkbox" value="${date}" onchange="updateRecoveryDateSelection('gameready', '${date}', this.checked)">
          ${new Date(date).toLocaleDateString()}
        `;
        gameReadyContainer.appendChild(label);
      });
    }

    function updateRecoveryDateSelection(type, date, checked) {
      if (checked) {
        if (!pastDatesData.recovery[type].dates.includes(date)) {
          pastDatesData.recovery[type].dates.push(date);
        }
      } else {
        pastDatesData.recovery[type].dates = pastDatesData.recovery[type].dates.filter(d => d !== date);
      }
    }

    // Initialize date pickers
    setupDatePicker('strength');
    setupDatePicker('training');
    setupDatePicker('recovery');

    // Rating sliders for past training
    $('#pastHittingRating').addEventListener('input', (e) => {
      $('#pastHittingRatingValue').textContent = e.target.value;
      pastDatesData.training.hitting.rating = parseInt(e.target.value);
    });

    $('#pastPitchingRating').addEventListener('input', (e) => {
      $('#pastPitchingRatingValue').textContent = e.target.value;
      pastDatesData.training.pitching.rating = parseInt(e.target.value);
    });

    // Focus inputs for past training
    const pastHittingFocusEl = $('#pastHittingFocus');
    const pastPitchingFocusEl = $('#pastPitchingFocus');

    console.log('Past hitting focus element:', pastHittingFocusEl);
    console.log('Past pitching focus element:', pastPitchingFocusEl);

    if (pastHittingFocusEl) {
      pastHittingFocusEl.addEventListener('input', (e) => {
        pastDatesData.training.hitting.focus = e.target.value;
        console.log('Updated hitting focus:', e.target.value);
        console.log('pastDatesData.training.hitting:', pastDatesData.training.hitting);
      });
    } else {
      console.error('pastHittingFocus element not found!');
    }

    if (pastPitchingFocusEl) {
      pastPitchingFocusEl.addEventListener('input', (e) => {
        pastDatesData.training.pitching.focus = e.target.value;
        console.log('Updated pitching focus:', e.target.value);
        console.log('pastDatesData.training.pitching:', pastDatesData.training.pitching);
      });
    } else {
      console.error('pastPitchingFocus element not found!');
    }

    // Recovery minutes inputs
    $('#pastHypMinutes').addEventListener('input', (e) => {
      pastDatesData.recovery.hyperbolic.minutes = parseInt(e.target.value) || 0;
    });

    $('#pastGameReadyMinutes').addEventListener('input', (e) => {
      pastDatesData.recovery.gameready.minutes = parseInt(e.target.value) || 0;
    });

    // --- Past Strength Lifts Management ---
    function addPastLift() {
      const container = $('#pastLifts');
      const liftIndex = pastDatesData.strength.lifts.length;

      const liftData = {
        name: '',
        sets: '',
        reps: '',
        weight: '',
        isMax: false,
        dates: []
      };

      pastDatesData.strength.lifts.push(liftData);

      const liftDiv = document.createElement('div');
      liftDiv.className = 'border-2 border-gray-700 rounded-lg p-4 mb-4';
      liftDiv.innerHTML = `
        <div class="grid grid-cols-4 gap-4 mb-4">
          <div>
            <label class="block text-sm font-semibold mb-1">Exercise</label>
            <input type="text" class="w-full border-2 rounded-lg p-2" placeholder="e.g., Bench Press"
                   onchange="updatePastLift(${liftIndex}, 'name', this.value)">
          </div>
          <div>
            <label class="block text-sm font-semibold mb-1">Sets</label>
            <input type="number" class="w-full border-2 rounded-lg p-2" placeholder="3"
                   onchange="updatePastLift(${liftIndex}, 'sets', this.value)">
          </div>
          <div>
            <label class="block text-sm font-semibold mb-1">Reps</label>
            <input type="number" class="w-full border-2 rounded-lg p-2" placeholder="8"
                   onchange="updatePastLift(${liftIndex}, 'reps', this.value)">
          </div>
          <div>
            <label class="block text-sm font-semibold mb-1">Weight (lbs)</label>
            <input type="number" class="w-full border-2 rounded-lg p-2" placeholder="135"
                   onchange="updatePastLift(${liftIndex}, 'weight', this.value)">
          </div>
        </div>
        <div class="mb-4">
          <button class="btn btn-muted" onclick="togglePastLiftMax(${liftIndex}, this)" data-max="0">
            Mark as MAX
          </button>
          <button class="btn btn-muted ml-2" onclick="removePastLift(${liftIndex})">
            Remove Lift
          </button>
        </div>
        <div class="mb-4">
          <label class="block text-lg font-semibold mb-2">Apply to dates:</label>
          <div id="pastLift${liftIndex}DateCheckboxes" class="flex flex-wrap gap-2">
            <!-- Date checkboxes will be populated here -->
          </div>
        </div>
      `;

      container.appendChild(liftDiv);
      updatePastLiftDateCheckboxes(liftIndex);
    }

    function updatePastLift(index, field, value) {
      if (pastDatesData.strength.lifts[index]) {
        pastDatesData.strength.lifts[index][field] = value;
      }
    }

    function togglePastLiftMax(index, button) {
      const isMax = button.dataset.max === '1';
      button.dataset.max = isMax ? '0' : '1';
      button.textContent = isMax ? 'Mark as MAX' : 'MAX ✓';
      button.classList.toggle('btn-primary', !isMax);
      button.classList.toggle('btn-muted', isMax);

      if (pastDatesData.strength.lifts[index]) {
        pastDatesData.strength.lifts[index].isMax = !isMax;
      }
    }

    function removePastLift(index) {
      pastDatesData.strength.lifts.splice(index, 1);
      renderPastLifts();
    }

    function renderPastLifts() {
      const container = $('#pastLifts');
      container.innerHTML = '';
      pastDatesData.strength.lifts.forEach((lift, index) => {
        // Re-create lift with current data
        addPastLift();
        // Populate with existing data
        const liftDiv = container.children[index];
        const inputs = liftDiv.querySelectorAll('input');
        inputs[0].value = lift.name || '';
        inputs[1].value = lift.sets || '';
        inputs[2].value = lift.reps || '';
        inputs[3].value = lift.weight || '';

        const maxBtn = liftDiv.querySelector('[data-max]');
        if (lift.isMax) {
          maxBtn.dataset.max = '1';
          maxBtn.textContent = 'MAX ✓';
          maxBtn.classList.add('btn-primary');
          maxBtn.classList.remove('btn-muted');
        }
      });
    }

    function updatePastLiftDateCheckboxes(liftIndex) {
      const dates = pastDatesData.strength.selectedDates;
      const container = $(`#pastLift${liftIndex}DateCheckboxes`);

      if (!container) return;

      container.innerHTML = '';
      dates.forEach(date => {
        const label = document.createElement('label');
        label.className = 'checkbox-label';
        label.innerHTML = `
          <input type="checkbox" value="${date}" onchange="updatePastLiftDateSelection(${liftIndex}, '${date}', this.checked)">
          ${new Date(date).toLocaleDateString()}
        `;
        container.appendChild(label);
      });
    }

    function updatePastLiftDateSelection(liftIndex, date, checked) {
      if (!pastDatesData.strength.lifts[liftIndex]) return;

      if (checked) {
        if (!pastDatesData.strength.lifts[liftIndex].dates.includes(date)) {
          pastDatesData.strength.lifts[liftIndex].dates.push(date);
        }
      } else {
        pastDatesData.strength.lifts[liftIndex].dates =
          pastDatesData.strength.lifts[liftIndex].dates.filter(d => d !== date);
      }
    }

    // Update all lift date checkboxes when strength dates change
    function updateAllPastLiftDateCheckboxes() {
      pastDatesData.strength.lifts.forEach((lift, index) => {
        updatePastLiftDateCheckboxes(index);
      });
    }

    // Override the strength date picker to also update lift checkboxes
    const originalStrengthSetup = setupDatePicker;
    setupDatePicker('strength');

    // Add event listener for add strength date button
    $('#addStrengthDate').addEventListener('click', () => {
      setTimeout(updateAllPastLiftDateCheckboxes, 100);
    });

    // Add past lift button
    $('#addPastLift').addEventListener('click', addPastLift);


  </script>
</body>
</html>